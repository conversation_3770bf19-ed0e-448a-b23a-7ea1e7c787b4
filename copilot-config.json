{"schemaVersion": 1, "name": "Vinyl Preflight Processor", "description": "Nástroj pro validaci hudebn<PERSON>ch projektů před masterováním", "capabilities": {"tools": {"listChanged": true}}, "tools": [{"name": "process-vinyl-preflight", "description": "Zpracuje adresář s hudebními projekty a vygeneruje validační report", "inputSchema": {"type": "object", "properties": {"source_directory": {"type": "string", "description": "C:/gz_projekt/data-for-testing"}}, "required": ["source_directory"]}}]}