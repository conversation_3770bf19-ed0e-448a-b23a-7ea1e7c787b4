FROM python:3.11-slim

WORKDIR /app

# Instalace systémových závislostí
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    unrar \
    && rm -rf /var/lib/apt/lists/*

# Kopírování requirements a instalace Python závislostí
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Kopírování aplikačního kódu
COPY . .

# Vytvoření výstupního adresáře
RUN mkdir -p /app/output

# Příkaz pro spuštění MCP serveru
CMD ["python", "vinyl_preflight_mcp_server.py"]